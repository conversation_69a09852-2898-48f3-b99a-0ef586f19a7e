# Clean System Implementation

This document describes the clean implementation of the recipe and product management system as requested.

## Overview

The system has been refactored to provide a cleaner, more maintainable approach to managing products, ingredients, and recipes. The implementation follows the user requirements exactly.

## Product Schema

The system uses the following product schema:

```typescript
interface Product {
  name: string
  ingredients: null | string // JSON string
  type: 'product' | 'ingredient' | 'recipe' // default: 'product'
  stock: number // default: 0
  total_amount: number // default: 0, calculated as total_amount * stock
  has_recipe: boolean // default: false
  is_liquid: boolean // default: false
}
```

## Key Features Implemented

### 1. Clean Recipe Creation System (`recipe-configuration.tsx`)

**Features:**
- Shows all ingredients with clear type indicators
- **Standard ingredients**: Display name + stock, disable Amount/Unit inputs (auto-calculated)
- **Ingredient-products**: Display name + total_amount, disable Available Stock input (auto-calculated)
- Proper stock deduction based on ingredient type

**Stock Deduction Logic:**
- **Standard ingredients**: Deduct from `stock` field
- **Ingredient-products**: Deduct from `total_amount` field
- Uses unified API endpoint with `operation: "deduct"`

**UI Improvements:**
- Clear visual indicators for ingredient types
- Disabled inputs for auto-calculated fields
- Better validation and error messages
- Toast notifications instead of alerts

### 2. Clean Product Creation System (`clean-product-creation.tsx`)

**Features:**
- **Liquid toggle**: `is_liquid` field with clear description
- **Ingredient toggle**: "Use this product as an ingredient in recipes?"
  - If enabled: Hides ingredient system, sets `type: 'ingredient'`
  - If disabled: Shows ingredient selection options, sets `type: 'product'`

**Ingredient Selection Options:**
- **"Use existing recipe"**: 
  - Select from available recipes
  - Shows recipe ingredients preview
  - Deducts stock from recipe ingredients when product is created
- **"Add individual ingredients"**:
  - Select from all ingredients (standard + ingredient-products)
  - Clear visual distinction between types
  - Real-time stock availability checking

**Amount per Unit Calculation:**
- For ingredient-type products: `total_amount = stock × amount_per_unit`
- Automatic calculation and display

### 3. Stock Deduction System

**Clean Logic:**
- **Standard ingredients**: Deduct from `stock` field
- **Ingredient-products**: Deduct from `total_amount` field
- **Liquid products**: Support for proportional deduction (e.g., 50ml from 750ml bottle)

**API Integration:**
- Uses unified `/api/products` PUT endpoint with `operation: "deduct"`
- API automatically handles standard vs ingredient-product logic
- Proper error handling and validation

## File Structure

### New Clean Components
- `src/app/(components)/clean-product-creation.tsx` - Clean product creation modal
- Updated `src/app/(components)/recipe-configuration.tsx` - Clean recipe creation

### Updated Components
- `src/app/stock/page.tsx` - Added clean product creation button and modal
- `src/app/(components)/stock-management.tsx` - Added legacy code comments

### API Endpoints
- `/api/products` - Handles unified stock deduction logic
- `/api/recipe` - Recipe creation and management

## Usage Instructions

### Creating a New Recipe
1. Go to Recipe Configuration page
2. Click "Añadir nueva receta"
3. Enter recipe name and amount
4. Add ingredients:
   - **Standard ingredients**: Select from dropdown, quantity/unit auto-managed
   - **Ingredient-products**: Select from dropdown, available stock auto-managed
5. System automatically deducts stock based on ingredient type

### Creating a New Product
1. Go to Stock page
2. Click "Nuevo Producto (Limpio)" (green button)
3. Fill basic product information
4. Configure toggles:
   - **Liquid toggle**: Enable for liquid products
   - **Ingredient toggle**: Enable to use as ingredient in recipes
5. If not using as ingredient, select ingredient method:
   - **Use existing recipe**: Select recipe, ingredients auto-populated
   - **Add individual ingredients**: Select ingredients individually

## Key Improvements

### Code Quality
- Removed duplicate logic across components
- Clear separation of concerns
- Better state management
- Consistent error handling

### User Experience
- Clear visual indicators for different product/ingredient types
- Disabled inputs for auto-calculated fields
- Better validation messages
- Toast notifications for better feedback

### System Logic
- Unified stock deduction API
- Proper handling of liquid vs non-liquid products
- Clear distinction between standard ingredients and ingredient-products
- Automatic total_amount calculation for ingredient-type products

## Migration Notes

The legacy code is still present but marked with comments:
- `===== LEGACY CODE - TO BE CLEANED UP =====`
- `===== LEGACY RECIPE CREATION - REPLACED BY CLEAN SYSTEM =====`
- `===== LEGACY PRODUCT FORM - REPLACED BY CLEAN SYSTEM =====`

These sections should be removed once the clean system is fully tested and validated.

## Testing

The system has been implemented with:
- No compilation errors
- Proper TypeScript types
- Clean component structure
- Unified API integration

The clean system is ready for testing and can be accessed via:
- Recipe creation: Recipe Configuration page
- Product creation: Stock page → "Nuevo Producto (Limpio)" button
