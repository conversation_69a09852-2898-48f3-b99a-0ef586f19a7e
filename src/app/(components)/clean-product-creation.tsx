"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { X, Plus } from "lucide-react";
import { toast } from "sonner";
import { useAppContext } from "@/context/AppContext";
import { categoryList } from "@/lib/utils";
import ImageUpload from "./image-upload";

interface CleanProductCreationProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onProductCreated: () => void;
}

interface ProductFormData {
  name: string;
  description: string;
  category: string;
  stock: number;
  purchase_price: number;
  sale_price: number;
  type: 'product' | 'ingredient';
  is_liquid: boolean;
  use_as_ingredient: boolean;
  total_amount?: number;
}

interface SelectedIngredient {
  name: string;
  quantity: string;
  unit: string;
  productId?: string;
  isStandard: boolean;
  isIngredientProduct: boolean;
  availableAmount: number;
}

export default function CleanProductCreation({
  open,
  onOpenChange,
  onProductCreated,
}: CleanProductCreationProps) {
  const { productsData, recipesData, fetchProducts, uploadImageToSupabase } = useAppContext();
  
  // CLEAN STATE MANAGEMENT
  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    category: "",
    stock: 0,
    purchase_price: 0,
    sale_price: 0,
    type: "product",
    is_liquid: false,
    use_as_ingredient: false,
  });

  const [amountPerUnit, setAmountPerUnit] = useState<number>(0);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // INGREDIENT SELECTION STATE
  const [ingredientSelectionMode, setIngredientSelectionMode] = useState<'recipe' | 'individual' | null>(null);
  const [selectedRecipeId, setSelectedRecipeId] = useState<string>("");
  const [selectedIngredients, setSelectedIngredients] = useState<SelectedIngredient[]>([]);
  
  // Individual ingredient form
  const [newIngredientName, setNewIngredientName] = useState("");
  const [newIngredientQuantity, setNewIngredientQuantity] = useState("");
  const [newIngredientUnit, setNewIngredientUnit] = useState("ml");

  // Add individual ingredient to list
  const addIndividualIngredient = () => {
    if (!newIngredientName || !newIngredientQuantity) {
      toast.error("Por favor completa el nombre y cantidad del ingrediente");
      return;
    }

    const typeInfo = getIngredientTypeInfo(newIngredientName);
    if (!typeInfo) {
      toast.error("No se encontró el ingrediente seleccionado");
      return;
    }

    const quantity = parseFloat(newIngredientQuantity);
    if (quantity <= 0) {
      toast.error("La cantidad debe ser mayor a 0");
      return;
    }

    // Check if ingredient already exists
    const exists = selectedIngredients.some(ing => ing.name === newIngredientName);
    if (exists) {
      toast.error("Este ingrediente ya está en la lista");
      return;
    }

    // Add to list
    const newIngredient: SelectedIngredient = {
      name: newIngredientName,
      quantity: newIngredientQuantity,
      unit: newIngredientUnit,
      productId: typeInfo.product.id,
      isStandard: typeInfo.isStandard,
      isIngredientProduct: typeInfo.isIngredientProduct,
      availableAmount: typeInfo.availableAmount,
    };

    setSelectedIngredients(prev => [...prev, newIngredient]);

    // Reset form
    setNewIngredientName("");
    setNewIngredientQuantity("");
    setNewIngredientUnit("ml");
  };

  // Helper function to get ingredient type info
  const getIngredientTypeInfo = (ingredientName: string) => {
    const matchingProduct = productsData.find(product =>
      product.name.toLowerCase().includes(ingredientName.toLowerCase()) ||
      ingredientName.toLowerCase().includes(product.name.toLowerCase())
    );

    if (!matchingProduct) return null;

    const isIngredientProduct = matchingProduct.type === 'ingredient';
    const isStandard = !isIngredientProduct;

    return {
      product: matchingProduct,
      isStandard,
      isIngredientProduct,
      isLiquid: matchingProduct.is_liquid === true,
      availableAmount: isIngredientProduct ? (matchingProduct.total_amount || 0) : matchingProduct.stock,
    };
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      category: "",
      stock: 0,
      purchase_price: 0,
      sale_price: 0,
      type: "product",
      is_liquid: false,
      use_as_ingredient: false,
    });
    setAmountPerUnit(0);
    setImageFile(null);
    setIngredientSelectionMode(null);
    setSelectedRecipeId("");
    setSelectedIngredients([]);
    setNewIngredientName("");
    setNewIngredientQuantity("");
    setNewIngredientUnit("ml");
  };

  // Handle image upload
  const handleImageUpload = async () => {
    if (!imageFile) return "";

    try {
      const fileName = `product-${Date.now()}.${imageFile.name.split(".").pop()}`;
      const uploadedUrl = await uploadImageToSupabase(imageFile, fileName);
      return uploadedUrl;
    } catch (error) {
      console.error("Error uploading image:", error);
      return "";
    }
  };

  // CLEAN PRODUCT CREATION LOGIC
  const handleCreateProduct = async () => {
    // Validation
    if (!formData.name.trim()) {
      toast.error("El nombre del producto es requerido");
      return;
    }

    if (!formData.category) {
      toast.error("La categoría es requerida");
      return;
    }

    if (formData.stock < 0) {
      toast.error("El stock no puede ser negativo");
      return;
    }

    setIsLoading(true);

    try {
      // Upload image if provided
      const imageUrl = await handleImageUpload();

      // Prepare ingredients data based on selection mode
      let ingredientsData = null;
      let hasRecipe = false;

      if (!formData.use_as_ingredient) {
        if (ingredientSelectionMode === 'recipe' && selectedRecipeId) {
          // Use existing recipe
          const selectedRecipe = recipesData.find(r => r.id.toString() === selectedRecipeId);
          if (selectedRecipe?.ingredients) {
            ingredientsData = selectedRecipe.ingredients;
            hasRecipe = true;
          }
        } else if (ingredientSelectionMode === 'individual' && selectedIngredients.length > 0) {
          // Use individual ingredients
          ingredientsData = selectedIngredients.map(ing => ({
            name: ing.name,
            quantity: ing.quantity,
            unit: ing.unit,
            productId: ing.productId,
            isStandard: ing.isStandard,
            isIngredientProduct: ing.isIngredientProduct,
          }));
          hasRecipe = true;
        }
      }

      // Calculate total_amount for ingredient products
      let totalAmount = null;
      if (formData.use_as_ingredient && amountPerUnit > 0) {
        totalAmount = formData.stock * amountPerUnit;
      }

      // Prepare product data
      const productData = {
        name: formData.name,
        description: formData.description,
        category: formData.category,
        stock: formData.stock,
        purchase_price: formData.purchase_price,
        sale_price: formData.sale_price,
        type: formData.use_as_ingredient ? 'ingredient' : 'product',
        is_liquid: formData.is_liquid,
        has_recipe: hasRecipe,
        ingredients: ingredientsData ? JSON.stringify(ingredientsData) : null,
        total_amount: totalAmount,
        image_url: imageUrl,
        updated_at: new Date().toISOString(),
      };

      // Create product
      const response = await fetch("/api/products", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(productData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create product");
      }

      // Handle stock deduction if using ingredients
      if (hasRecipe && formData.stock > 0) {
        await handleStockDeduction(ingredientsData, formData.stock);
      }

      toast.success("Producto creado exitosamente");
      resetForm();
      onOpenChange(false);
      onProductCreated();
    } catch (error) {
      console.error("Error creating product:", error);
      toast.error(error instanceof Error ? error.message : "Error creating product");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle stock deduction for ingredients
  const handleStockDeduction = async (ingredients: any[], productAmount: number) => {
    if (!ingredients || ingredients.length === 0) return;

    try {
      for (const ingredient of ingredients) {
        if (!ingredient.productId) continue;

        const requiredQuantity = parseFloat(ingredient.quantity || "1") * productAmount;

        // Use the unified deduction API
        const response = await fetch("/api/products", {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            id: ingredient.productId,
            stock: requiredQuantity,
            operation: "deduct"
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.warn(`Failed to deduct stock for ${ingredient.name}: ${errorData.error}`);
        }
      }
    } catch (error) {
      console.error("Error deducting stock:", error);
    }
  };

  // Handle form field changes
  const handleFormChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle liquid toggle
  const handleLiquidToggle = (checked: boolean) => {
    handleFormChange('is_liquid', checked);
  };

  // Handle ingredient toggle
  const handleIngredientToggle = (checked: boolean) => {
    handleFormChange('use_as_ingredient', checked);
    if (checked) {
      // If enabled, hide ingredient system
      setIngredientSelectionMode(null);
      setSelectedIngredients([]);
      setSelectedRecipeId("");
      // Set type to ingredient
      handleFormChange('type', 'ingredient');
    } else {
      // If disabled, show ingredient selection options
      handleFormChange('type', 'product');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Crear Nuevo Producto</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Basic Product Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre del Producto</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                placeholder="Ej: Vodka Absolut 750ml"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descripción</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
                placeholder="Descripción del producto"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Categoría</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => handleFormChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccionar categoría" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryList.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock">Stock Inicial</Label>
                <Input
                  id="stock"
                  type="number"
                  min="0"
                  value={formData.stock}
                  onChange={(e) => handleFormChange('stock', parseInt(e.target.value) || 0)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="purchase_price">Precio de Compra</Label>
                <Input
                  id="purchase_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.purchase_price}
                  onChange={(e) => handleFormChange('purchase_price', parseFloat(e.target.value) || 0)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sale_price">Precio de Venta</Label>
                <Input
                  id="sale_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.sale_price}
                  onChange={(e) => handleFormChange('sale_price', parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>
          </div>

          {/* CLEAN TOGGLES SECTION */}
          <div className="space-y-4 border-t pt-4">
            <h3 className="text-lg font-medium">Configuración del Producto</h3>

            {/* Liquid Toggle */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>¿Es un producto líquido?</Label>
                <p className="text-sm text-muted-foreground">
                  Los productos líquidos tienen seguimiento de volumen y deducción proporcional
                </p>
              </div>
              <Switch
                checked={formData.is_liquid}
                onCheckedChange={handleLiquidToggle}
              />
            </div>

            {/* Ingredient Toggle */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>¿Usar este producto como ingrediente en recetas?</Label>
                <p className="text-sm text-muted-foreground">
                  Si se habilita, este producto se puede usar como ingrediente
                </p>
              </div>
              <Switch
                checked={formData.use_as_ingredient}
                onCheckedChange={handleIngredientToggle}
              />
            </div>

            {/* Amount per unit for ingredient products */}
            {formData.use_as_ingredient && (
              <div className="space-y-2">
                <Label htmlFor="amount_per_unit">Cantidad por Unidad</Label>
                <Input
                  id="amount_per_unit"
                  type="number"
                  min="0"
                  value={amountPerUnit}
                  onChange={(e) => setAmountPerUnit(parseFloat(e.target.value) || 0)}
                  placeholder="Ej: 750 (para 750ml por botella)"
                />
                <p className="text-sm text-muted-foreground">
                  Total amount = stock × cantidad por unidad
                </p>
              </div>
            )}
          </div>

          {/* INGREDIENT SELECTION SECTION - Only show if NOT using as ingredient */}
          {!formData.use_as_ingredient && (
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium">Ingredientes del Producto</h3>

              {/* Ingredient Selection Mode */}
              <div className="space-y-3">
                <Label>Seleccionar método de ingredientes:</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant={ingredientSelectionMode === 'recipe' ? 'default' : 'outline'}
                    onClick={() => setIngredientSelectionMode('recipe')}
                    size="sm"
                  >
                    Usar receta existente
                  </Button>
                  <Button
                    type="button"
                    variant={ingredientSelectionMode === 'individual' ? 'default' : 'outline'}
                    onClick={() => setIngredientSelectionMode('individual')}
                    size="sm"
                  >
                    Agregar ingredientes individuales
                  </Button>
                </div>
              </div>

              {/* Recipe Selection */}
              {ingredientSelectionMode === 'recipe' && (
                <div className="space-y-3">
                  <Label>Seleccionar Receta:</Label>
                  <Select
                    value={selectedRecipeId}
                    onValueChange={setSelectedRecipeId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar receta" />
                    </SelectTrigger>
                    <SelectContent>
                      {recipesData
                        .filter(recipe => recipe.type === 'recipe')
                        .map((recipe) => (
                          <SelectItem key={recipe.id} value={recipe.id.toString()}>
                            <div className="flex justify-between items-center w-full">
                              <span>{recipe.name}</span>
                              <Badge variant="outline" className="ml-2">
                                {recipe.ingredients?.length || 0} ingredientes
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>

                  {/* Show selected recipe ingredients */}
                  {selectedRecipeId && (() => {
                    const selectedRecipe = recipesData.find(r => r.id.toString() === selectedRecipeId);
                    if (!selectedRecipe?.ingredients) return null;

                    return (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-medium text-blue-900 mb-2">Ingredientes de la receta:</h4>
                        <div className="space-y-2">
                          {selectedRecipe.ingredients.map((ingredient: any, index: number) => (
                            <div key={index} className="flex justify-between text-sm">
                              <span>{ingredient.name}</span>
                              <span className="text-blue-600">
                                {ingredient.quantity} {ingredient.unit}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* Individual Ingredients Selection */}
              {ingredientSelectionMode === 'individual' && (
                <div className="space-y-4">
                  <Label>Agregar Ingredientes Individuales:</Label>

                  {/* Add ingredient form */}
                  <div className="grid grid-cols-4 gap-2 items-end">
                    <div>
                      <Label className="text-xs">Ingrediente</Label>
                      <Select
                        value={newIngredientName}
                        onValueChange={setNewIngredientName}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Seleccionar" />
                        </SelectTrigger>
                        <SelectContent>
                          {/* Standard Ingredients */}
                          {productsData
                            .filter(product => product.type !== 'ingredient' && product.type !== 'recipe')
                            .map((product) => (
                              <SelectItem key={`std-${product.id}`} value={product.name}>
                                <div className="flex justify-between items-center w-full">
                                  <span className="text-sm">{product.name}</span>
                                  <Badge variant="outline" className="text-xs ml-2">
                                    Stock: {product.stock}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}

                          {/* Ingredient Products */}
                          {productsData
                            .filter(product => product.type === 'ingredient')
                            .map((product) => (
                              <SelectItem key={`ing-${product.id}`} value={product.name}>
                                <div className="flex justify-between items-center w-full">
                                  <span className="text-sm">{product.name}</span>
                                  <Badge variant="outline" className="text-xs ml-2 bg-blue-50">
                                    Total: {product.total_amount || 0}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-xs">Cantidad</Label>
                      <Input
                        type="number"
                        min="0"
                        value={newIngredientQuantity}
                        onChange={(e) => setNewIngredientQuantity(e.target.value)}
                        className="h-8"
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label className="text-xs">Unidad</Label>
                      <Select
                        value={newIngredientUnit}
                        onValueChange={setNewIngredientUnit}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ml">ml</SelectItem>
                          <SelectItem value="g">g</SelectItem>
                          <SelectItem value="unidad">unidad</SelectItem>
                          <SelectItem value="oz">oz</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Button
                      type="button"
                      size="sm"
                      onClick={addIndividualIngredient}
                      className="h-8"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Selected ingredients list */}
                  {selectedIngredients.length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Ingredientes seleccionados:</Label>
                      <div className="space-y-2">
                        {selectedIngredients.map((ingredient, index) => (
                          <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">{ingredient.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {ingredient.isIngredientProduct ? 'Ingredient Product' : 'Standard'}
                              </Badge>
                              <span className="text-sm text-gray-600">
                                {ingredient.quantity} {ingredient.unit}
                              </span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedIngredients(prev => prev.filter((_, i) => i !== index))}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleCreateProduct} disabled={isLoading}>
            {isLoading ? "Creando..." : "Crear Producto"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
